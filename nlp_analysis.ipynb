import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer, PorterStemmer, SnowballStemmer
from nltk.corpus import stopwords
from nltk.chunk import ne_chunk
import spacy

# Download necessary NLTK resources
nltk.download("punkt")
nltk.download("stopwords")
nltk.download("wordnet")
nltk.download("averaged_perceptron_tagger_eng")  # tagger POS
nltk.download("maxent_ne_chunker")  # NER chunker
nltk.download("maxent_ne_chunker_tab")  # NER chunker tables
nltk.download("words")  # word corpus for NER

# Input text
text = "Last week, <PERSON> traveled from London to New York for a meeting with TechCorp. She stayed at the Hilton Hotel and prepared a presentation about the new AI software her team developed. On Friday, she met with <PERSON>, the CEO of TechCorp, to discuss a potential partnership. The meeting went well, but <PERSON> was exhausted after the long flight. She plans to visit Paris next month for another conference."

print("Original text:")
print(text)

# Podział tekstu na zdania
sentences = sent_tokenize(text)
print("Podział na zdania:")
for i, sentence in enumerate(sentences):
    print(f"Zdanie {i+1}: {sentence}")

# Tokenizacja każdego zdania
print("Tokenizacja zdań:")
for i, sentence in enumerate(sentences):
    tokens = word_tokenize(sentence)
    print(f"Zdanie {i+1} tokeny: {tokens}")

# Tokenizacja całego tekstu
all_tokens = word_tokenize(text)
print("Wszystkie tokeny:")
print(all_tokens)
print(f"Liczba tokenów: {len(all_tokens)}")

# Wybrane zdanie: "On Friday, she met with Robert Davis, the CEO of TechCorp, to discuss a potential partnership."
selected_sentence = sentences[2]  # Trzecie zdanie w tekście
print("Etap 2: Analiza morfologiczna (POS tagging)")
print(f'Wybrane zdanie: "{selected_sentence}"')

# Tokenizacja wybranego zdania
tokens = word_tokenize(selected_sentence)

# POS tagging
tagged_tokens = nltk.pos_tag(tokens)
print(f"\nTokeny: {tokens}")
print(f"Tagged tokens: {tagged_tokens}")

# Wyjaśnienie znaczenia tagów POS
pos_descriptions = {
    "CC": "Coordinating conjunction",
    "CD": "Cardinal number",
    "DT": "Determiner",
    "EX": "Existential there",
    "FW": "Foreign word",
    "IN": "Preposition or subordinating conjunction",
    "JJ": "Adjective",
    "JJR": "Adjective, comparative",
    "JJS": "Adjective, superlative",
    "LS": "List item marker",
    "MD": "Modal",
    "NN": "Noun, singular or mass",
    "NNS": "Noun, plural",
    "NNP": "Proper noun, singular",
    "NNPS": "Proper noun, plural",
    "PDT": "Predeterminer",
    "POS": "Possessive ending",
    "PRP": "Personal pronoun",
    "PRP$": "Possessive pronoun",
    "RB": "Adverb",
    "RBR": "Adverb, comparative",
    "RBS": "Adverb, superlative",
    "RP": "Particle",
    "SYM": "Symbol",
    "TO": "to",
    "UH": "Interjection",
    "VB": "Verb, base form",
    "VBD": "Verb, past tense",
    "VBG": "Verb, gerund or present participle",
    "VBN": "Verb, past participle",
    "VBP": "Verb, non-3rd person singular present",
    "VBZ": "Verb, 3rd person singular present",
    "WDT": "Wh-determiner",
    "WP": "Wh-pronoun",
    "WP$": "Possessive wh-pronoun",
    "WRB": "Wh-adverb",
    ",": "Comma",
    ".": "Period",
    ":": "Colon or semicolon",
    "$": "Dollar sign",
    "#": "Pound sign",
    '"': "Quote",
    "(": "Left parenthesis",
    ")": "Right parenthesis",
}

print("Tokeny z oznaczeniami części mowy:")
for token, tag in tagged_tokens:
    print(f"{token}: {tag} - {pos_descriptions.get(tag, 'Unknown tag')}")

# Wybrane zdanie: "She plans to visit Paris next month for another conference."
lemma_sentence = sentences[4]  # Piąte zdanie w tekście
print("Etap 3: Lematyzacja")
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
lemma_tokens = word_tokenize(lemma_sentence)

# POS tagging dla lematyzacji
lemma_tagged = nltk.pos_tag(lemma_tokens)

# Inicjalizacja lematyzatora
lemmatizer = WordNetLemmatizer()

print(f"\nTokeny: {lemma_tokens}")
print(f"Tagged tokens: {lemma_tagged}")

# Funkcja do konwersji tagów NLTK na tagi WordNet
def get_wordnet_pos(tag):
    if tag.startswith("J"):
        return "a"  # adjective
    elif tag.startswith("V"):
        return "v"  # verb
    elif tag.startswith("N"):
        return "n"  # noun
    elif tag.startswith("R"):
        return "r"  # adverb
    else:
        return "n"  # default to noun

# Lematyzacja z uwzględnieniem części mowy
lemmatized_tokens = []
for token, tag in lemma_tagged:
    wordnet_pos = get_wordnet_pos(tag)
    lemma = lemmatizer.lemmatize(token, pos=wordnet_pos)
    lemmatized_tokens.append((token, lemma, tag))

# Wyświetlenie wyników lematyzacji
print("Wyniki lematyzacji:")
print("Token | Lemat | POS Tag")
print("-" * 40)
for token, lemma, tag in lemmatized_tokens:
    print(f"{token} | {lemma} | {tag}")

# Porównanie oryginalnego zdania z lematyzowanym
original_tokens = [token for token, _, _ in lemmatized_tokens]
lemmatized_text = [lemma for _, lemma, _ in lemmatized_tokens]

print("Zdanie oryginalne:")
print(" ".join(original_tokens))
print("\nZdanie po lematyzacji:")
print(" ".join(lemmatized_text))

print("=" * 60)
print("Etap 4: Stemming")
print("=" * 60)
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
stem_tokens = word_tokenize(lemma_sentence)

# Inicjalizacja stemmerów
porter_stemmer = PorterStemmer()
snowball_stemmer = SnowballStemmer('english')

print(f"\nTokeny: {stem_tokens}")

# Stemming z Porter Stemmer
porter_stemmed = []
for token in stem_tokens:
    stemmed = porter_stemmer.stem(token)
    porter_stemmed.append((token, stemmed))

# Wyświetlenie wyników Porter Stemmer
print("Wyniki Porter Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in porter_stemmed:
    print(f"{token} | {stem}")

# Stemming z Snowball Stemmer
snowball_stemmed = []
for token in stem_tokens:
    stemmed = snowball_stemmer.stem(token)
    snowball_stemmed.append((token, stemmed))

# Wyświetlenie wyników Snowball Stemmer
print("Wyniki Snowball Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in snowball_stemmed:
    print(f"{token} | {stem}")

# Porównanie zdań po stemmingu
porter_text = [stem for _, stem in porter_stemmed]
snowball_text = [stem for _, stem in snowball_stemmed]

print("Zdanie oryginalne:")
print(" ".join(stem_tokens))
print("\nZdanie po Porter Stemming:")
print(" ".join(porter_text))
print("\nZdanie po Snowball Stemming:")
print(" ".join(snowball_text))

print("=" * 60)
print("Porównanie Stemming vs Lematyzacja")
print("=" * 60)
print("Token | Porter Stem | Snowball Stem | Lemma")
print("-" * 50)

# Pobieramy lematy z poprzedniego etapu dla porównania
lemma_dict = {token: lemma for token, lemma, _ in lemmatized_tokens}

for token in stem_tokens:
    porter_stem = porter_stemmer.stem(token)
    snowball_stem = snowball_stemmer.stem(token)
    lemma = lemma_dict.get(token, token)  # Jeśli nie ma lematu, użyj oryginalnego tokenu
    print(f"{token} | {porter_stem} | {snowball_stem} | {lemma}")

print("=" * 60)
print("Etap 5: Analiza stop words")
print("=" * 60)

# Pobieranie listy stop words z NLTK
english_stopwords = set(stopwords.words('english'))
print(f"Liczba stop words w języku angielskim: {len(english_stopwords)}")
print(f"Przykłady stop words: {list(english_stopwords)[:20]}")

# Analiza stop words w naszym tekście
print(f"Oryginalny tekst:\n{text}")
all_tokens_lower = [token.lower() for token in all_tokens]

print(f"\nWszystkie tokeny (małe litery): {all_tokens_lower}")
print(f"Liczba wszystkich tokenów: {len(all_tokens_lower)}")

# Identyfikacja stop words w tekście
found_stopwords = []
content_words = []

for token in all_tokens_lower:
    if token in english_stopwords:
        found_stopwords.append(token)
    else:
        content_words.append(token)

print(f"Znalezione stop words w tekście:")
print(f"Stop words: {found_stopwords}")
print(f"Liczba stop words: {len(found_stopwords)}")

print(f"\nSłowa treściowe (bez stop words):")
print(f"Content words: {content_words}")
print(f"Liczba słów treściowych: {len(content_words)}")

# Statystyki stop words
total_words = len(all_tokens_lower)
stopwords_count = len(found_stopwords)
content_words_count = len(content_words)
stopwords_percentage = (stopwords_count / total_words) * 100

print(f"Statystyki:")
print(f"Całkowita liczba tokenów: {total_words}")
print(f"Liczba stop words: {stopwords_count}")
print(f"Liczba słów treściowych: {content_words_count}")
print(f"Procent stop words: {stopwords_percentage:.1f}%")

# Wersja 1: Oryginalny tekst (bez zmian)
original_text_tokens = all_tokens
print(f"Wersja 1 - Tekst oryginalny (bez zmian):")
print(f"Tokeny: {original_text_tokens}")
print(f"Liczba tokenów: {len(original_text_tokens)}")

# Wersja 2: Tekst bez stop words (zachowujemy wielkość liter i interpunkcję)
filtered_tokens = []
for token in all_tokens:
    if token.lower() not in english_stopwords:
        filtered_tokens.append(token)

print(f"Wersja 2 - Tekst bez stop words:")
print(f"Tokeny: {filtered_tokens}")
print(f"Liczba tokenów: {len(filtered_tokens)}")

# Rekonstrukcja tekstów
original_reconstructed = " ".join(original_text_tokens)
filtered_reconstructed = " ".join(filtered_tokens)

print(f"Rekonstrukcja tekstów:")
print(f"\nTekst oryginalny:")
print(f'"{original_reconstructed}"')
print(f"\nTekst bez stop words:")
print(f'"{filtered_reconstructed}"')

# Analiza wpływu usunięcia stop words
reduction_percentage = ((len(original_text_tokens) - len(filtered_tokens)) / len(original_text_tokens)) * 100
print(f"Wpływ usunięcia stop words:")
print(f"Redukcja liczby tokenów: {reduction_percentage:.1f}%")
print(f"Usunięte tokeny: {len(original_text_tokens) - len(filtered_tokens)}")

# Przygotowanie danych do dalszych etapów analizy
print(f"\nPrzygotowane dane do dalszych etapów:")
print(f"1. original_tokens - lista tokenów oryginalnych ({len(original_text_tokens)} elementów)")
print(f"2. filtered_tokens - lista tokenów bez stop words ({len(filtered_tokens)} elementów)")
print(f"3. original_text - tekst oryginalny")
print(f"4. filtered_text - tekst bez stop words")

# Zapisanie zmiennych do użycia w kolejnych etapach
original_tokens = original_text_tokens
filtered_text = filtered_reconstructed

print(f"Zmienne przygotowane do dalszej analizy:")
print(f"- original_tokens: {len(original_tokens)} tokenów")
print(f"- filtered_tokens: {len(filtered_tokens)} tokenów") 
print(f"- original_text: tekst oryginalny")
print(f"- filtered_text: tekst bez stop words")

print(f"\n✅ Dane gotowe do użycia w kolejnych etapach analizy (wektoryzacja, mapa słów, itp.)")

print("=" * 70)
print("Etap 6: Rozpoznawanie jednostek nazwanych (NER)")
print("=" * 70)

print(f"Analizowany tekst:\n{text}")

print("-" * 50)
print("CZĘŚĆ 1: NER z NLTK")
print("-" * 50)

# Tokenizacja i POS tagging dla całego tekstu
all_tokens_for_ner = word_tokenize(text)
pos_tagged_for_ner = nltk.pos_tag(all_tokens_for_ner)

print(f"Tokeny z tagami POS (pierwsze 10):")
for i, (token, tag) in enumerate(pos_tagged_for_ner[:10]):
    print(f"{token}: {tag}")
print("... (i więcej)")

# NER z NLTK
nltk_ner_tree = ne_chunk(pos_tagged_for_ner)
print(f"Wynik NER z NLTK (struktura drzewa):")
print(nltk_ner_tree)

# Ekstrakcja jednostek nazwanych z drzewa NLTK
def extract_entities_from_tree(tree):
    entities = []
    for subtree in tree:
        if hasattr(subtree, 'label'):  # To jest jednostka nazwana
            entity_name = ' '.join([token for token, pos in subtree.leaves()])
            entity_label = subtree.label()
            entities.append((entity_name, entity_label))
    return entities

nltk_entities = extract_entities_from_tree(nltk_ner_tree)
print(f"Jednostki nazwane znalezione przez NLTK:")
if nltk_entities:
    for entity, label in nltk_entities:
        print(f"- {entity}: {label}")
else:
    print("- Brak jednostek nazwanych znalezionych przez NLTK")

print("-" * 50)
print("CZĘŚĆ 2: NER z spaCy")
print("-" * 50)

try:
    # Próba załadowania modelu spaCy
    try:
        nlp = spacy.load("en_core_web_sm")
        print("✅ Model spaCy 'en_core_web_sm' załadowany pomyślnie")
    except OSError:
        print("⚠️  Model 'en_core_web_sm' nie jest zainstalowany")
        print("Próba załadowania podstawowego modelu...")
        nlp = spacy.load("en_core_web_md")
        print("✅ Model spaCy 'en_core_web_md' załadowany pomyślnie")
    
    # Analiza tekstu z spaCy
    doc = nlp(text)
    
    print(f"\nJednostki nazwane znalezione przez spaCy:")
    spacy_entities = []
    if doc.ents:
        for ent in doc.ents:
            spacy_entities.append((ent.text, ent.label_, ent.start_char, ent.end_char))
            print(f"- {ent.text}: {ent.label_} ({spacy.explain(ent.label_)}) [pozycja: {ent.start_char}-{ent.end_char}]")
    else:
        print("- Brak jednostek nazwanych znalezionych przez spaCy")
        
except Exception as e:
    print(f"❌ Błąd podczas ładowania spaCy: {e}")
    print("Sprawdź czy spaCy jest zainstalowane: pip install spacy")
    print("I pobierz model: python -m spacy download en_core_web_sm")
    spacy_entities = []

# Analiza szczegółowa jednostek
expected_entities = {
    'Emily Johnson': 'PERSON',
    'London': 'GPE',
    'New York': 'GPE', 
    'TechCorp': 'ORG',
    'Hilton Hotel': 'ORG',
    'Friday': 'DATE',
    'Robert Davis': 'PERSON',
    'Paris': 'GPE'
}

print(f"Oczekiwane jednostki vs znalezione przez spaCy:")
print("Jednostka | Oczekiwany typ | Status | Typ spaCy")
print("-" * 60)

try:
    found_entities = {ent.text: ent.label_ for ent in doc.ents}
    
    for expected_entity, expected_type in expected_entities.items():
        if expected_entity in found_entities:
            found_type = found_entities[expected_entity]
            status = "✅" if found_type == expected_type else "⚠️"
            print(f"{expected_entity} | {expected_type} | {status} | {found_type}")
        else:
            print(f"{expected_entity} | {expected_type} | ❌ | Nie znaleziono")
except:
    print("Analiza niedostępna - spaCy nie załadowany")

print("-" * 50)
print("PORÓWNANIE NLTK vs spaCy")
print("-" * 50)

print(f"Podsumowanie wyników:")
print(f"- NLTK znalazł: {len(nltk_entities)} jednostek")

try:
    print(f"- spaCy znalazł: {len(spacy_entities)} jednostek")
    
    # Analiza pokrycia
    nltk_entity_texts = {entity for entity, _ in nltk_entities}
    spacy_entity_texts = {ent[0] for ent in spacy_entities}
    
    common_entities = nltk_entity_texts.intersection(spacy_entity_texts)
    print(f"- Wspólne jednostki: {len(common_entities)}")
    if common_entities:
        print(f"  Wspólne: {list(common_entities)}")
    
    only_nltk = nltk_entity_texts - spacy_entity_texts
    only_spacy = spacy_entity_texts - nltk_entity_texts
    
    if only_nltk:
        print(f"- Tylko NLTK: {list(only_nltk)}")
    if only_spacy:
        print(f"- Tylko spaCy: {list(only_spacy)}")
        
except:
    print("- spaCy: Analiza niedostępna")

# Przygotowanie danych do dalszych etapów
print(f"\n✅ Dane NER przygotowane do dalszej analizy:")
print(f"- nltk_entities: {len(nltk_entities)} jednostek z NLTK")
try:
    print(f"- spacy_entities: {len(spacy_entities)} jednostek z spaCy")
    print(f"- doc: obiekt spaCy Doc do dalszej analizy")
    print(f"- Jednostki gotowe do wizualizacji i dalszej analizy")
except:
    print("- spacy_entities: Niedostępne (spaCy nie załadowany)")
    print(f"- Dane NLTK dostępne do dalszej analizy")