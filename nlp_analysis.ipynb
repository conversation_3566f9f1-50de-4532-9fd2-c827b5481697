import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer, PorterStemmer, SnowballStemmer
from nltk.corpus import stopwords
from nltk.chunk import ne_chunk
import spacy
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud

# Download necessary NLTK resources
nltk.download("punkt")
nltk.download("stopwords")
nltk.download("wordnet")
nltk.download("averaged_perceptron_tagger_eng")  # tagger POS
nltk.download("maxent_ne_chunker")  # NER chunker
nltk.download("maxent_ne_chunker_tab")  # NER chunker tables
nltk.download("words")  # word corpus for NER

# Input text
text = "Last week, <PERSON> traveled from London to New York for a meeting with TechCorp. She stayed at the Hilton Hotel and prepared a presentation about the new AI software her team developed. On Friday, she met with <PERSON>, the CEO of TechCorp, to discuss a potential partnership. The meeting went well, but <PERSON> was exhausted after the long flight. She plans to visit Paris next month for another conference."

print("Original text:")
print(text)

# Podział tekstu na zdania
sentences = sent_tokenize(text)
print("Podział na zdania:")
for i, sentence in enumerate(sentences):
    print(f"Zdanie {i+1}: {sentence}")

# Tokenizacja każdego zdania
print("Tokenizacja zdań:")
for i, sentence in enumerate(sentences):
    tokens = word_tokenize(sentence)
    print(f"Zdanie {i+1} tokeny: {tokens}")

# Tokenizacja całego tekstu
all_tokens = word_tokenize(text)
print("Wszystkie tokeny:")
print(all_tokens)
print(f"Liczba tokenów: {len(all_tokens)}")

# Wybrane zdanie: "On Friday, she met with Robert Davis, the CEO of TechCorp, to discuss a potential partnership."
selected_sentence = sentences[2]  # Trzecie zdanie w tekście
print("Etap 2: Analiza morfologiczna (POS tagging)")
print(f'Wybrane zdanie: "{selected_sentence}"')

# Tokenizacja wybranego zdania
tokens = word_tokenize(selected_sentence)

# POS tagging
tagged_tokens = nltk.pos_tag(tokens)
print(f"\nTokeny: {tokens}")
print(f"Tagged tokens: {tagged_tokens}")

# Wyjaśnienie znaczenia tagów POS
pos_descriptions = {
    "CC": "Coordinating conjunction",
    "CD": "Cardinal number",
    "DT": "Determiner",
    "EX": "Existential there",
    "FW": "Foreign word",
    "IN": "Preposition or subordinating conjunction",
    "JJ": "Adjective",
    "JJR": "Adjective, comparative",
    "JJS": "Adjective, superlative",
    "LS": "List item marker",
    "MD": "Modal",
    "NN": "Noun, singular or mass",
    "NNS": "Noun, plural",
    "NNP": "Proper noun, singular",
    "NNPS": "Proper noun, plural",
    "PDT": "Predeterminer",
    "POS": "Possessive ending",
    "PRP": "Personal pronoun",
    "PRP$": "Possessive pronoun",
    "RB": "Adverb",
    "RBR": "Adverb, comparative",
    "RBS": "Adverb, superlative",
    "RP": "Particle",
    "SYM": "Symbol",
    "TO": "to",
    "UH": "Interjection",
    "VB": "Verb, base form",
    "VBD": "Verb, past tense",
    "VBG": "Verb, gerund or present participle",
    "VBN": "Verb, past participle",
    "VBP": "Verb, non-3rd person singular present",
    "VBZ": "Verb, 3rd person singular present",
    "WDT": "Wh-determiner",
    "WP": "Wh-pronoun",
    "WP$": "Possessive wh-pronoun",
    "WRB": "Wh-adverb",
    ",": "Comma",
    ".": "Period",
    ":": "Colon or semicolon",
    "$": "Dollar sign",
    "#": "Pound sign",
    '"': "Quote",
    "(": "Left parenthesis",
    ")": "Right parenthesis",
}

print("Tokeny z oznaczeniami części mowy:")
for token, tag in tagged_tokens:
    print(f"{token}: {tag} - {pos_descriptions.get(tag, 'Unknown tag')}")

# Wybrane zdanie: "She plans to visit Paris next month for another conference."
lemma_sentence = sentences[4]  # Piąte zdanie w tekście
print("Etap 3: Lematyzacja")
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
lemma_tokens = word_tokenize(lemma_sentence)

# POS tagging dla lematyzacji
lemma_tagged = nltk.pos_tag(lemma_tokens)

# Inicjalizacja lematyzatora
lemmatizer = WordNetLemmatizer()

print(f"\nTokeny: {lemma_tokens}")
print(f"Tagged tokens: {lemma_tagged}")

# Funkcja do konwersji tagów NLTK na tagi WordNet
def get_wordnet_pos(tag):
    if tag.startswith("J"):
        return "a"  # adjective
    elif tag.startswith("V"):
        return "v"  # verb
    elif tag.startswith("N"):
        return "n"  # noun
    elif tag.startswith("R"):
        return "r"  # adverb
    else:
        return "n"  # default to noun

# Lematyzacja z uwzględnieniem części mowy
lemmatized_tokens = []
for token, tag in lemma_tagged:
    wordnet_pos = get_wordnet_pos(tag)
    lemma = lemmatizer.lemmatize(token, pos=wordnet_pos)
    lemmatized_tokens.append((token, lemma, tag))

# Wyświetlenie wyników lematyzacji
print("Wyniki lematyzacji:")
print("Token | Lemat | POS Tag")
print("-" * 40)
for token, lemma, tag in lemmatized_tokens:
    print(f"{token} | {lemma} | {tag}")

# Porównanie oryginalnego zdania z lematyzowanym
original_tokens = [token for token, _, _ in lemmatized_tokens]
lemmatized_text = [lemma for _, lemma, _ in lemmatized_tokens]

print("Zdanie oryginalne:")
print(" ".join(original_tokens))
print("\nZdanie po lematyzacji:")
print(" ".join(lemmatized_text))

print("=" * 60)
print("Etap 4: Stemming")
print("=" * 60)
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
stem_tokens = word_tokenize(lemma_sentence)

# Inicjalizacja stemmerów
porter_stemmer = PorterStemmer()
snowball_stemmer = SnowballStemmer('english')

print(f"\nTokeny: {stem_tokens}")

# Stemming z Porter Stemmer
porter_stemmed = []
for token in stem_tokens:
    stemmed = porter_stemmer.stem(token)
    porter_stemmed.append((token, stemmed))

# Wyświetlenie wyników Porter Stemmer
print("Wyniki Porter Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in porter_stemmed:
    print(f"{token} | {stem}")

# Stemming z Snowball Stemmer
snowball_stemmed = []
for token in stem_tokens:
    stemmed = snowball_stemmer.stem(token)
    snowball_stemmed.append((token, stemmed))

# Wyświetlenie wyników Snowball Stemmer
print("Wyniki Snowball Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in snowball_stemmed:
    print(f"{token} | {stem}")

# Porównanie zdań po stemmingu
porter_text = [stem for _, stem in porter_stemmed]
snowball_text = [stem for _, stem in snowball_stemmed]

print("Zdanie oryginalne:")
print(" ".join(stem_tokens))
print("\nZdanie po Porter Stemming:")
print(" ".join(porter_text))
print("\nZdanie po Snowball Stemming:")
print(" ".join(snowball_text))

print("=" * 60)
print("Porównanie Stemming vs Lematyzacja")
print("=" * 60)
print("Token | Porter Stem | Snowball Stem | Lemma")
print("-" * 50)

# Pobieramy lematy z poprzedniego etapu dla porównania
lemma_dict = {token: lemma for token, lemma, _ in lemmatized_tokens}

for token in stem_tokens:
    porter_stem = porter_stemmer.stem(token)
    snowball_stem = snowball_stemmer.stem(token)
    lemma = lemma_dict.get(token, token)  # Jeśli nie ma lematu, użyj oryginalnego tokenu
    print(f"{token} | {porter_stem} | {snowball_stem} | {lemma}")

print("=" * 60)
print("Etap 5: Analiza stop words")
print("=" * 60)

# Pobieranie listy stop words z NLTK
english_stopwords = set(stopwords.words('english'))
print(f"Liczba stop words w języku angielskim: {len(english_stopwords)}")
print(f"Przykłady stop words: {list(english_stopwords)[:20]}")

# Analiza stop words w naszym tekście
print(f"Oryginalny tekst:\n{text}")
all_tokens_lower = [token.lower() for token in all_tokens]

print(f"\nWszystkie tokeny (małe litery): {all_tokens_lower}")
print(f"Liczba wszystkich tokenów: {len(all_tokens_lower)}")

# Identyfikacja stop words w tekście
found_stopwords = []
content_words = []

for token in all_tokens_lower:
    if token in english_stopwords:
        found_stopwords.append(token)
    else:
        content_words.append(token)

print(f"Znalezione stop words w tekście:")
print(f"Stop words: {found_stopwords}")
print(f"Liczba stop words: {len(found_stopwords)}")

print(f"\nSłowa treściowe (bez stop words):")
print(f"Content words: {content_words}")
print(f"Liczba słów treściowych: {len(content_words)}")

# Statystyki stop words
total_words = len(all_tokens_lower)
stopwords_count = len(found_stopwords)
content_words_count = len(content_words)
stopwords_percentage = (stopwords_count / total_words) * 100

print(f"Statystyki:")
print(f"Całkowita liczba tokenów: {total_words}")
print(f"Liczba stop words: {stopwords_count}")
print(f"Liczba słów treściowych: {content_words_count}")
print(f"Procent stop words: {stopwords_percentage:.1f}%")

# Wersja 1: Oryginalny tekst (bez zmian)
original_text_tokens = all_tokens
print(f"Wersja 1 - Tekst oryginalny (bez zmian):")
print(f"Tokeny: {original_text_tokens}")
print(f"Liczba tokenów: {len(original_text_tokens)}")

# Wersja 2: Tekst bez stop words (zachowujemy wielkość liter i interpunkcję)
filtered_tokens = []
for token in all_tokens:
    if token.lower() not in english_stopwords:
        filtered_tokens.append(token)

print(f"Wersja 2 - Tekst bez stop words:")
print(f"Tokeny: {filtered_tokens}")
print(f"Liczba tokenów: {len(filtered_tokens)}")

# Rekonstrukcja tekstów
original_reconstructed = " ".join(original_text_tokens)
filtered_reconstructed = " ".join(filtered_tokens)

print(f"Rekonstrukcja tekstów:")
print(f"\nTekst oryginalny:")
print(f'"{original_reconstructed}"')
print(f"\nTekst bez stop words:")
print(f'"{filtered_reconstructed}"')

# Analiza wpływu usunięcia stop words
reduction_percentage = ((len(original_text_tokens) - len(filtered_tokens)) / len(original_text_tokens)) * 100
print(f"Wpływ usunięcia stop words:")
print(f"Redukcja liczby tokenów: {reduction_percentage:.1f}%")
print(f"Usunięte tokeny: {len(original_text_tokens) - len(filtered_tokens)}")

# Przygotowanie danych do dalszych etapów analizy
print(f"\nPrzygotowane dane do dalszych etapów:")
print(f"1. original_tokens - lista tokenów oryginalnych ({len(original_text_tokens)} elementów)")
print(f"2. filtered_tokens - lista tokenów bez stop words ({len(filtered_tokens)} elementów)")
print(f"3. original_text - tekst oryginalny")
print(f"4. filtered_text - tekst bez stop words")

# Zapisanie zmiennych do użycia w kolejnych etapach
original_tokens = original_text_tokens
filtered_text = filtered_reconstructed

print(f"Zmienne przygotowane do dalszej analizy:")
print(f"- original_tokens: {len(original_tokens)} tokenów")
print(f"- filtered_tokens: {len(filtered_tokens)} tokenów") 
print(f"- original_text: tekst oryginalny")
print(f"- filtered_text: tekst bez stop words")

print(f"\n✅ Dane gotowe do użycia w kolejnych etapach analizy (wektoryzacja, mapa słów, itp.)")

print("=" * 70)
print("Etap 6: Rozpoznawanie jednostek nazwanych (NER)")
print("=" * 70)

print(f"Analizowany tekst:\n{text}")

print("-" * 50)
print("CZĘŚĆ 1: NER z NLTK")
print("-" * 50)

# Tokenizacja i POS tagging dla całego tekstu
all_tokens_for_ner = word_tokenize(text)
pos_tagged_for_ner = nltk.pos_tag(all_tokens_for_ner)

print(f"Tokeny z tagami POS (pierwsze 10):")
for i, (token, tag) in enumerate(pos_tagged_for_ner[:10]):
    print(f"{token}: {tag}")
print("... (i więcej)")

# NER z NLTK
nltk_ner_tree = ne_chunk(pos_tagged_for_ner)
print(f"Wynik NER z NLTK (struktura drzewa):")
print(nltk_ner_tree)

# Ekstrakcja jednostek nazwanych z drzewa NLTK
def extract_entities_from_tree(tree):
    entities = []
    for subtree in tree:
        if hasattr(subtree, 'label'):  # To jest jednostka nazwana
            entity_name = ' '.join([token for token, pos in subtree.leaves()])
            entity_label = subtree.label()
            entities.append((entity_name, entity_label))
    return entities

nltk_entities = extract_entities_from_tree(nltk_ner_tree)
print(f"Jednostki nazwane znalezione przez NLTK:")
if nltk_entities:
    for entity, label in nltk_entities:
        print(f"- {entity}: {label}")
else:
    print("- Brak jednostek nazwanych znalezionych przez NLTK")

print("-" * 50)
print("CZĘŚĆ 2: NER z spaCy")
print("-" * 50)

try:
    # Próba załadowania modelu spaCy
    try:
        nlp = spacy.load("en_core_web_sm")
        print("✅ Model spaCy 'en_core_web_sm' załadowany pomyślnie")
    except OSError:
        print("⚠️  Model 'en_core_web_sm' nie jest zainstalowany")
        print("Próba załadowania podstawowego modelu...")
        nlp = spacy.load("en_core_web_md")
        print("✅ Model spaCy 'en_core_web_md' załadowany pomyślnie")
    
    # Analiza tekstu z spaCy
    doc = nlp(text)
    
    print(f"\nJednostki nazwane znalezione przez spaCy:")
    spacy_entities = []
    if doc.ents:
        for ent in doc.ents:
            spacy_entities.append((ent.text, ent.label_, ent.start_char, ent.end_char))
            print(f"- {ent.text}: {ent.label_} ({spacy.explain(ent.label_)}) [pozycja: {ent.start_char}-{ent.end_char}]")
    else:
        print("- Brak jednostek nazwanych znalezionych przez spaCy")
        
except Exception as e:
    print(f"❌ Błąd podczas ładowania spaCy: {e}")
    print("Sprawdź czy spaCy jest zainstalowane: pip install spacy")
    print("I pobierz model: python -m spacy download en_core_web_sm")
    spacy_entities = []

# Analiza szczegółowa jednostek
expected_entities = {
    'Emily Johnson': 'PERSON',
    'London': 'GPE',
    'New York': 'GPE', 
    'TechCorp': 'ORG',
    'Hilton Hotel': 'ORG',
    'Friday': 'DATE',
    'Robert Davis': 'PERSON',
    'Paris': 'GPE'
}

print(f"Oczekiwane jednostki vs znalezione przez spaCy:")
print("Jednostka | Oczekiwany typ | Status | Typ spaCy")
print("-" * 60)

try:
    found_entities = {ent.text: ent.label_ for ent in doc.ents}
    
    for expected_entity, expected_type in expected_entities.items():
        if expected_entity in found_entities:
            found_type = found_entities[expected_entity]
            status = "✅" if found_type == expected_type else "⚠️"
            print(f"{expected_entity} | {expected_type} | {status} | {found_type}")
        else:
            print(f"{expected_entity} | {expected_type} | ❌ | Nie znaleziono")
except:
    print("Analiza niedostępna - spaCy nie załadowany")

print("-" * 50)
print("PORÓWNANIE NLTK vs spaCy")
print("-" * 50)

print(f"Podsumowanie wyników:")
print(f"- NLTK znalazł: {len(nltk_entities)} jednostek")

try:
    print(f"- spaCy znalazł: {len(spacy_entities)} jednostek")
    
    # Analiza pokrycia
    nltk_entity_texts = {entity for entity, _ in nltk_entities}
    spacy_entity_texts = {ent[0] for ent in spacy_entities}
    
    common_entities = nltk_entity_texts.intersection(spacy_entity_texts)
    print(f"- Wspólne jednostki: {len(common_entities)}")
    if common_entities:
        print(f"  Wspólne: {list(common_entities)}")
    
    only_nltk = nltk_entity_texts - spacy_entity_texts
    only_spacy = spacy_entity_texts - nltk_entity_texts
    
    if only_nltk:
        print(f"- Tylko NLTK: {list(only_nltk)}")
    if only_spacy:
        print(f"- Tylko spaCy: {list(only_spacy)}")
        
except:
    print("- spaCy: Analiza niedostępna")

# Przygotowanie danych do dalszych etapów
print(f"\n✅ Dane NER przygotowane do dalszej analizy:")
print(f"- nltk_entities: {len(nltk_entities)} jednostek z NLTK")
try:
    print(f"- spacy_entities: {len(spacy_entities)} jednostek z spaCy")
    print(f"- doc: obiekt spaCy Doc do dalszej analizy")
    print(f"- Jednostki gotowe do wizualizacji i dalszej analizy")
except:
    print("- spacy_entities: Niedostępne (spaCy nie załadowany)")
    print(f"- Dane NLTK dostępne do dalszej analizy")

print("=" * 70)
print("Etap 7: Wektoryzacja tekstu")
print("=" * 70)

# Przygotowanie danych - używamy wyników z poprzednich etapów
print("Przygotowanie danych do wektoryzacji:")
print(f"- Tekst oryginalny: {len(text.split())} słów")
print(f"- Tekst bez stop words: {len(filtered_text.split())} słów")

# Przygotowanie korpusu - podzielimy tekst na zdania dla lepszej analizy
original_sentences = sentences  # z etapu tokenizacji
filtered_sentences = []

# Tworzenie wersji zdań bez stop words
english_stopwords = set(stopwords.words('english'))
for sentence in original_sentences:
    tokens = word_tokenize(sentence)
    filtered_tokens = [token for token in tokens if token.lower() not in english_stopwords]
    filtered_sentences.append(' '.join(filtered_tokens))

print(f"\nPrzygotowano {len(original_sentences)} zdań do analizy")

print("-" * 50)
print("CZĘŚĆ 1: Bag of Words (BoW)")
print("-" * 50)

# BoW dla tekstu oryginalnego
bow_vectorizer_original = CountVectorizer(lowercase=True, token_pattern=r'\b\w+\b')
bow_matrix_original = bow_vectorizer_original.fit_transform(original_sentences)
bow_feature_names_original = bow_vectorizer_original.get_feature_names_out()

print(f"BoW - Tekst oryginalny:")
print(f"- Liczba unikalnych słów (słownik): {len(bow_feature_names_original)}")
print(f"- Wymiary macierzy: {bow_matrix_original.shape}")
print(f"- Gęstość macierzy: {bow_matrix_original.nnz / (bow_matrix_original.shape[0] * bow_matrix_original.shape[1]):.3f}")

# BoW dla tekstu bez stop words
bow_vectorizer_filtered = CountVectorizer(lowercase=True, token_pattern=r'\b\w+\b')
bow_matrix_filtered = bow_vectorizer_filtered.fit_transform(filtered_sentences)
bow_feature_names_filtered = bow_vectorizer_filtered.get_feature_names_out()

print(f"\nBoW - Tekst bez stop words:")
print(f"- Liczba unikalnych słów (słownik): {len(bow_feature_names_filtered)}")
print(f"- Wymiary macierzy: {bow_matrix_filtered.shape}")
print(f"- Gęstość macierzy: {bow_matrix_filtered.nnz / (bow_matrix_filtered.shape[0] * bow_matrix_filtered.shape[1]):.3f}")

# Analiza najczęstszych słów
def analyze_word_frequencies(vectorizer, matrix, title):
    # Suma wystąpień każdego słowa w całym korpusie
    word_freq = np.array(matrix.sum(axis=0)).flatten()
    feature_names = vectorizer.get_feature_names_out()
    
    # Tworzenie DataFrame dla łatwiejszej analizy
    freq_df = pd.DataFrame({
        'word': feature_names,
        'frequency': word_freq
    }).sort_values('frequency', ascending=False)
    
    print(f"\n{title} - Top 10 najczęstszych słów:")
    print(freq_df.head(10).to_string(index=False))
    
    return freq_df

# Analiza dla obu wersji
freq_original = analyze_word_frequencies(bow_vectorizer_original, bow_matrix_original, "Tekst oryginalny")
freq_filtered = analyze_word_frequencies(bow_vectorizer_filtered, bow_matrix_filtered, "Tekst bez stop words")

# Wizualizacja porównania częstości słów
plt.figure(figsize=(15, 6))

# Wykres dla tekstu oryginalnego
plt.subplot(1, 2, 1)
top_words_orig = freq_original.head(10)
plt.barh(range(len(top_words_orig)), top_words_orig['frequency'])
plt.yticks(range(len(top_words_orig)), top_words_orig['word'])
plt.xlabel('Częstość wystąpień')
plt.title('Top 10 słów - Tekst oryginalny')
plt.gca().invert_yaxis()

# Wykres dla tekstu bez stop words
plt.subplot(1, 2, 2)
top_words_filt = freq_filtered.head(10)
plt.barh(range(len(top_words_filt)), top_words_filt['frequency'])
plt.yticks(range(len(top_words_filt)), top_words_filt['word'])
plt.xlabel('Częstość wystąpień')
plt.title('Top 10 słów - Bez stop words')
plt.gca().invert_yaxis()

plt.tight_layout()
plt.show()

print("\n📊 Obserwacje BoW:")
print(f"- Usunięcie stop words zmniejszyło słownik o {len(bow_feature_names_original) - len(bow_feature_names_filtered)} słów")
print(f"- Redukcja słownika: {((len(bow_feature_names_original) - len(bow_feature_names_filtered)) / len(bow_feature_names_original) * 100):.1f}%")
print(f"- Tekst bez stop words skupia się na słowach treściowych")

print("-" * 50)
print("CZĘŚĆ 2: TF-IDF")
print("-" * 50)

# TF-IDF dla tekstu oryginalnego
tfidf_vectorizer_original = TfidfVectorizer(lowercase=True, token_pattern=r'\b\w+\b')
tfidf_matrix_original = tfidf_vectorizer_original.fit_transform(original_sentences)
tfidf_feature_names_original = tfidf_vectorizer_original.get_feature_names_out()

print(f"TF-IDF - Tekst oryginalny:")
print(f"- Liczba unikalnych słów: {len(tfidf_feature_names_original)}")
print(f"- Wymiary macierzy: {tfidf_matrix_original.shape}")

# TF-IDF dla tekstu bez stop words
tfidf_vectorizer_filtered = TfidfVectorizer(lowercase=True, token_pattern=r'\b\w+\b')
tfidf_matrix_filtered = tfidf_vectorizer_filtered.fit_transform(filtered_sentences)
tfidf_feature_names_filtered = tfidf_vectorizer_filtered.get_feature_names_out()

print(f"\nTF-IDF - Tekst bez stop words:")
print(f"- Liczba unikalnych słów: {len(tfidf_feature_names_filtered)}")
print(f"- Wymiary macierzy: {tfidf_matrix_filtered.shape}")

# Analiza najważniejszych słów według TF-IDF
def analyze_tfidf_scores(vectorizer, matrix, title):
    # Średnie wyniki TF-IDF dla każdego słowa
    mean_tfidf = np.array(matrix.mean(axis=0)).flatten()
    feature_names = vectorizer.get_feature_names_out()
    
    # Tworzenie DataFrame
    tfidf_df = pd.DataFrame({
        'word': feature_names,
        'avg_tfidf': mean_tfidf
    }).sort_values('avg_tfidf', ascending=False)
    
    print(f"\n{title} - Top 10 słów według TF-IDF:")
    print(tfidf_df.head(10).to_string(index=False))
    
    return tfidf_df

# Analiza dla obu wersji
tfidf_original = analyze_tfidf_scores(tfidf_vectorizer_original, tfidf_matrix_original, "Tekst oryginalny")
tfidf_filtered = analyze_tfidf_scores(tfidf_vectorizer_filtered, tfidf_matrix_filtered, "Tekst bez stop words")

# Wizualizacja wyników TF-IDF
plt.figure(figsize=(15, 6))

# Wykres dla tekstu oryginalnego
plt.subplot(1, 2, 1)
top_tfidf_orig = tfidf_original.head(10)
plt.barh(range(len(top_tfidf_orig)), top_tfidf_orig['avg_tfidf'])
plt.yticks(range(len(top_tfidf_orig)), top_tfidf_orig['word'])
plt.xlabel('Średni wynik TF-IDF')
plt.title('Top 10 słów TF-IDF - Tekst oryginalny')
plt.gca().invert_yaxis()

# Wykres dla tekstu bez stop words
plt.subplot(1, 2, 2)
top_tfidf_filt = tfidf_filtered.head(10)
plt.barh(range(len(top_tfidf_filt)), top_tfidf_filt['avg_tfidf'])
plt.yticks(range(len(top_tfidf_filt)), top_tfidf_filt['word'])
plt.xlabel('Średni wynik TF-IDF')
plt.title('Top 10 słów TF-IDF - Bez stop words')
plt.gca().invert_yaxis()

plt.tight_layout()
plt.show()

print("\n📊 Obserwacje TF-IDF:")
print(f"- TF-IDF automatycznie obniża wagi częstych słów (jak stop words)")
print(f"- Słowa charakterystyczne dla tekstu otrzymują wyższe wagi")
print(f"- Usunięcie stop words czyni wyniki bardziej czytelne")

# Wybieramy zdanie do szczegółowej analizy
target_sentence = "She plans to visit Paris next month for another conference."
print(f"Analiza zdania: '{target_sentence}'")

# Znajdowanie indeksu zdania
sentence_idx = None
for i, sentence in enumerate(original_sentences):
    if target_sentence in sentence:
        sentence_idx = i
        break

if sentence_idx is not None:
    print(f"\nZdanie znalezione na pozycji {sentence_idx}")
    
    # BoW dla tego zdania
    bow_vector_orig = bow_matrix_original[sentence_idx].toarray().flatten()
    bow_vector_filt = bow_matrix_filtered[sentence_idx].toarray().flatten()
    
    # TF-IDF dla tego zdania
    tfidf_vector_orig = tfidf_matrix_original[sentence_idx].toarray().flatten()
    tfidf_vector_filt = tfidf_matrix_filtered[sentence_idx].toarray().flatten()
    
    # Analiza słów w zdaniu
    print(f"\nAnaliza wektoryzacji dla wybranego zdania:")
    
    # Słowa z niezerowymi wartościami w BoW (oryginalny)
    nonzero_bow_orig = np.nonzero(bow_vector_orig)[0]
    print(f"\nBoW (oryginalny) - słowa w zdaniu:")
    for idx in nonzero_bow_orig:
        word = bow_feature_names_original[idx]
        count = bow_vector_orig[idx]
        print(f"  {word}: {count}")
    
    # Słowa z niezerowymi wartościami w TF-IDF (oryginalny)
    nonzero_tfidf_orig = np.nonzero(tfidf_vector_orig)[0]
    print(f"\nTF-IDF (oryginalny) - wagi słów:")
    tfidf_words_orig = [(tfidf_feature_names_original[idx], tfidf_vector_orig[idx]) for idx in nonzero_tfidf_orig]
    tfidf_words_orig.sort(key=lambda x: x[1], reverse=True)
    for word, score in tfidf_words_orig:
        print(f"  {word}: {score:.4f}")
        
else:
    print("Zdanie nie zostało znalezione w korpusie")

print("-" * 50)
print("CZĘŚĆ 3: Word Embeddings")
print("-" * 50)

# Analiza zdania z użyciem spaCy embeddings
target_sentence = "She plans to visit Paris next month for another conference."
print(f"Analiza embeddings dla zdania: '{target_sentence}'")

try:
    # Używamy modelu spaCy z poprzedniego etapu
    if 'nlp' in locals():
        print("✅ Używam załadowanego modelu spaCy")
    else:
        print("⚠️ Ładuję model spaCy...")
        nlp = spacy.load("en_core_web_sm")
    
    # Przetwarzanie zdania
    doc = nlp(target_sentence)
    
    print(f"\nAnaliza embeddings słów w zdaniu:")
    print(f"Wymiar wektorów: {doc[0].vector.shape[0]}")
    
    # Analiza każdego słowa
    word_vectors = {}
    print(f"\nWektory słów (pierwsze 5 wymiarów):")
    for token in doc:
        if not token.is_punct and not token.is_space:
            word_vectors[token.text] = token.vector
            print(f"{token.text}: [{', '.join([f'{x:.3f}' for x in token.vector[:5]])}...]")
    
except Exception as e:
    print(f"❌ Błąd z spaCy embeddings: {e}")
    print("Sprawdź czy model spaCy z wektorami jest zainstalowany")
    word_vectors = {}

# Analiza podobieństwa semantycznego między słowami
if word_vectors:
    print("\n🔍 Analiza podobieństwa semantycznego:")
    
    # Wybieramy kluczowe słowa do analizy
    key_words = ['visit', 'conference', 'Paris', 'plans', 'month']
    available_words = [word for word in key_words if word in word_vectors]
    
    if len(available_words) >= 2:
        print(f"\nMacierz podobieństwa cosinusowego:")
        
        # Tworzenie macierzy podobieństwa
        similarity_matrix = np.zeros((len(available_words), len(available_words)))
        
        for i, word1 in enumerate(available_words):
            for j, word2 in enumerate(available_words):
                if i != j:
                    # Obliczanie podobieństwa cosinusowego
                    vec1 = word_vectors[word1].reshape(1, -1)
                    vec2 = word_vectors[word2].reshape(1, -1)
                    similarity = cosine_similarity(vec1, vec2)[0][0]
                    similarity_matrix[i][j] = similarity
                else:
                    similarity_matrix[i][j] = 1.0
        
        # Tworzenie DataFrame dla lepszej wizualizacji
        similarity_df = pd.DataFrame(similarity_matrix, 
                                   index=available_words, 
                                   columns=available_words)
        
        print(similarity_df.round(3))
        
        # Wizualizacja macierzy podobieństwa
        plt.figure(figsize=(8, 6))
        sns.heatmap(similarity_df, annot=True, cmap='coolwarm', center=0, 
                   square=True, fmt='.3f')
        plt.title('Macierz podobieństwa semantycznego słów')
        plt.tight_layout()
        plt.show()
        
        # Znajdowanie najbardziej podobnych par słów
        print(f"\n🎯 Najbardziej podobne pary słów:")
        similarities = []
        for i, word1 in enumerate(available_words):
            for j, word2 in enumerate(available_words):
                if i < j:  # Unikamy duplikatów
                    sim = similarity_matrix[i][j]
                    similarities.append((word1, word2, sim))
        
        similarities.sort(key=lambda x: x[2], reverse=True)
        for word1, word2, sim in similarities[:3]:
            print(f"  {word1} ↔ {word2}: {sim:.3f}")
    
    else:
        print("Zbyt mało słów do analizy podobieństwa")
else:
    print("Brak dostępnych wektorów słów")

# Analiza wpływu lematyzacji i stemmingu na jakość embeddings
print("\n🔬 Wpływ lematyzacji i stemmingu na embeddings:")

if word_vectors:
    # Przykładowe słowa do analizy
    test_words = {
        'plans': {'lemma': 'plan', 'stem_porter': 'plan', 'stem_snowball': 'plan'},
        'conference': {'lemma': 'conference', 'stem_porter': 'confer', 'stem_snowball': 'confer'}
    }
    
    for original_word, variants in test_words.items():
        if original_word in word_vectors:
            print(f"\nAnaliza dla słowa '{original_word}':")
            original_vec = word_vectors[original_word]
            
            # Sprawdzamy każdy wariant
            for variant_type, variant_word in variants.items():
                try:
                    # Przetwarzamy wariant przez spaCy
                    variant_doc = nlp(variant_word)
                    if len(variant_doc) > 0:
                        variant_vec = variant_doc[0].vector
                        
                        # Obliczamy podobieństwo
                        similarity = cosine_similarity(
                            original_vec.reshape(1, -1), 
                            variant_vec.reshape(1, -1)
                        )[0][0]
                        
                        print(f"  {variant_type} ('{variant_word}'): podobieństwo = {similarity:.3f}")
                        
                except Exception as e:
                    print(f"  {variant_type}: błąd - {e}")
    
    print(f"\n💡 Wnioski:")
    print(f"- Lematyzacja zazwyczaj zachowuje wysokie podobieństwo semantyczne")
    print(f"- Stemming może zmniejszać podobieństwo, szczególnie dla słów złożonych")
    print(f"- Word embeddings są odporne na małe zmiany morfologiczne")
    print(f"- Dla analizy semantycznej lepiej używać oryginalnych form lub lematów")

else:
    print("Analiza niedostępna - brak wektorów słów")

print("=" * 70)
print("PODSUMOWANIE WEKTORYZACJI TEKSTU")
print("=" * 70)

print(f"""
📊 PORÓWNANIE METOD WEKTORYZACJI:

1. 🎒 BAG OF WORDS (BoW):
   ✅ Zalety:
   - Prosty w implementacji i interpretacji
   - Szybki w obliczeniach
   - Dobry dla klasyfikacji tekstu
   
   ❌ Wady:
   - Ignoruje kolejność słów
   - Wysokowymiarowe, rzadkie wektory
   - Brak informacji semantycznej

2. 📈 TF-IDF:
   ✅ Zalety:
   - Automatycznie obniża wagi częstych słów
   - Wyróżnia słowa charakterystyczne
   - Lepszy niż BoW dla wyszukiwania
   
   ❌ Wady:
   - Nadal ignoruje kolejność i semantykę
   - Wysokowymiarowe wektory
   - Zależny od korpusu

3. 🧠 WORD EMBEDDINGS:
   ✅ Zalety:
   - Gęste, niskowymiarowe wektory
   - Zachowuje relacje semantyczne
   - Pretrenowane modele dostępne
   
   ❌ Wady:
   - Wymaga dużych modeli
   - Mniej interpretowalny
   - Może wymagać fine-tuningu

🎯 WPŁYW USUNIĘCIA STOP WORDS:
- BoW: Znacząca redukcja wymiarowości, skupienie na treści
- TF-IDF: Czytelniejsze wyniki, lepsze wyróżnienie kluczowych słów
- Embeddings: Mniejszy wpływ, ale może poprawić fokus semantyczny

🔧 REKOMENDACJE:
- Klasyfikacja tekstu: TF-IDF bez stop words
- Analiza semantyczna: Word embeddings z lematyzacją
- Wyszukiwanie: TF-IDF z filtrowaniem stop words
- Analiza sentymentu: Embeddings z kontekstem
""")

# Przygotowanie danych do dalszych etapów
print(f"\n✅ Dane wektoryzacji przygotowane:")
print(f"- bow_matrix_original: {bow_matrix_original.shape}")
print(f"- bow_matrix_filtered: {bow_matrix_filtered.shape}")
print(f"- tfidf_matrix_original: {tfidf_matrix_original.shape}")
print(f"- tfidf_matrix_filtered: {tfidf_matrix_filtered.shape}")
if word_vectors:
    print(f"- word_vectors: {len(word_vectors)} słów z embeddings")
print(f"\n🚀 Gotowe do dalszej analizy: klasyfikacji, klasteryzacji, wizualizacji!")

print("=" * 70)
print("Etap 8: Generowanie mapy słów (Word Cloud)")
print("=" * 70)

# Przygotowanie danych - używamy wyników z poprzednich etapów
print("Przygotowanie danych do generowania word cloud:")
print(f"- Tekst oryginalny: {len(text.split())} słów")
print(f"- Tekst bez stop words: {len(filtered_text.split())} słów")

# Sprawdzenie dostępności danych
if 'text' in locals() and 'filtered_text' in locals():
    print("✅ Dane z poprzednich etapów dostępne")
    print(f"- Tekst oryginalny zawiera: {len(text.split())} słów")
    print(f"- Tekst filtrowany zawiera: {len(filtered_text.split())} słów")
else:
    print("⚠️ Przygotowuję dane...")
    # Fallback - jeśli dane nie są dostępne
    text = "Last week, Emily Johnson traveled from London to New York for a meeting with TechCorp. She stayed at the Hilton Hotel and prepared a presentation about the new AI software her team developed. On Friday, she met with Robert Davis, the CEO of TechCorp, to discuss a potential partnership. The meeting went well, but Emily was exhausted after the long flight. She plans to visit Paris next month for another conference."
    
    # Tworzenie wersji bez stop words
    english_stopwords = set(stopwords.words('english'))
    tokens = word_tokenize(text)
    filtered_tokens = [token for token in tokens if token.lower() not in english_stopwords and token.isalpha()]
    filtered_text = ' '.join(filtered_tokens)
    
    print(f"✅ Dane przygotowane:")
    print(f"- Tekst oryginalny: {len(text.split())} słów")
    print(f"- Tekst bez stop words: {len(filtered_text.split())} słów")

# Sprawdzenie czy tekst oryginalny rzeczywiście zawiera stop words
english_stopwords = set(stopwords.words('english'))
original_tokens = word_tokenize(text.lower())
stop_words_in_original = [token for token in original_tokens if token in english_stopwords]

print(f"\n🔍 Weryfikacja zawartości:")
print(f"- Stop words w tekście oryginalnym: {len(stop_words_in_original)} ({len(set(stop_words_in_original))} unikalnych)")
print(f"- Przykłady stop words: {list(set(stop_words_in_original))[:10]}")

if len(stop_words_in_original) == 0:
    print("⚠️ UWAGA: Tekst oryginalny nie zawiera stop words - może być już przefiltrowany!")
    # Jeśli tekst jest już przefiltrowany, przywróćmy oryginalny tekst
    text = "Last week, Emily Johnson traveled from London to New York for a meeting with TechCorp. She stayed at the Hilton Hotel and prepared a presentation about the new AI software her team developed. On Friday, she met with Robert Davis, the CEO of TechCorp, to discuss a potential partnership. The meeting went well, but Emily was exhausted after the long flight. She plans to visit Paris next month for another conference."
    
    # Ponowne utworzenie wersji bez stop words z przywróconego tekstu
    tokens = word_tokenize(text)
    filtered_tokens = [token for token in tokens if token.lower() not in english_stopwords and token.isalpha()]
    filtered_text = ' '.join(filtered_tokens)
    
    print("✅ Przywrócono oryginalny tekst z stop words")
    print(f"✅ Ponownie utworzono tekst bez stop words")
    
    # Ponowna weryfikacja
    original_tokens_new = word_tokenize(text.lower())
    stop_words_in_original_new = [token for token in original_tokens_new if token in english_stopwords]
    print(f"📊 Nowa weryfikacja:")
    print(f"- Tekst oryginalny: {len(text.split())} słów")
    print(f"- Stop words w oryginalnym: {len(stop_words_in_original_new)}")
    print(f"- Tekst bez stop words: {len(filtered_text.split())} słów")
    print(f"- Przykłady stop words: {list(set(stop_words_in_original_new))[:10]}")

# Konfiguracja parametrów Word Cloud
wordcloud_config = {
    'width': 800,
    'height': 400,
    'max_words': 100,
    'background_color': 'white',
    'colormap': 'viridis',
    'relative_scaling': 0.5,
    'min_font_size': 10,
    'max_font_size': 100,
    'random_state': 42  # dla powtarzalnych wyników
}

print("Konfiguracja Word Cloud:")
for key, value in wordcloud_config.items():
    print(f"- {key}: {value}")

print(f"\n📊 Parametry wyjaśnione:")
print(f"- width/height: Rozmiar obrazu w pikselach")
print(f"- max_words: Maksymalna liczba słów do wyświetlenia")
print(f"- colormap: Paleta kolorów (viridis = zielono-niebieska)")
print(f"- relative_scaling: Jak bardzo rozmiar zależy od częstości (0-1)")
print(f"- random_state: Zapewnia powtarzalne układy słów")

print("-" * 50)
print("WORD CLOUD 1: Tekst oryginalny")
print("-" * 50)

# Generowanie word cloud dla tekstu oryginalnego
try:
    wordcloud_original = WordCloud(**wordcloud_config).generate(text)
    
    # Wyświetlanie word cloud
    plt.figure(figsize=(12, 6))
    plt.imshow(wordcloud_original, interpolation='bilinear')
    plt.axis('off')
    plt.title('Word Cloud - Tekst oryginalny (z stop words)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()
    
    # Analiza najczęstszych słów
    word_freq_original = wordcloud_original.words_
    print(f"\n📈 Top 10 słów w tekście oryginalnym:")
    sorted_words = sorted(word_freq_original.items(), key=lambda x: x[1], reverse=True)
    for i, (word, freq) in enumerate(sorted_words[:10]):
        print(f"{i+1:2d}. {word}: {freq:.3f}")
    
    print(f"\n🔍 Obserwacje:")
    print(f"- Całkowita liczba unikalnych słów: {len(word_freq_original)}")
    print(f"- Stop words prawdopodobnie dominują (the, to, and, etc.)")
    print(f"- Słowa treściowe mogą być mniej widoczne")
    
except Exception as e:
    print(f"❌ Błąd podczas generowania word cloud: {e}")
    print("Sprawdź czy biblioteka wordcloud jest zainstalowana: pip install wordcloud")

print("-" * 50)
print("WORD CLOUD 2: Tekst bez stop words")
print("-" * 50)

# Generowanie word cloud dla tekstu bez stop words
try:
    # Używamy innej palety kolorów dla rozróżnienia
    wordcloud_config_filtered = wordcloud_config.copy()
    wordcloud_config_filtered['colormap'] = 'plasma'  # Fioletowo-różowa paleta
    
    wordcloud_filtered = WordCloud(**wordcloud_config_filtered).generate(filtered_text)
    
    # Wyświetlanie word cloud
    plt.figure(figsize=(12, 6))
    plt.imshow(wordcloud_filtered, interpolation='bilinear')
    plt.axis('off')
    plt.title('Word Cloud - Tekst bez stop words', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()
    
    # Analiza najczęstszych słów
    word_freq_filtered = wordcloud_filtered.words_
    print(f"\n📈 Top 10 słów w tekście bez stop words:")
    sorted_words_filtered = sorted(word_freq_filtered.items(), key=lambda x: x[1], reverse=True)
    for i, (word, freq) in enumerate(sorted_words_filtered[:10]):
        print(f"{i+1:2d}. {word}: {freq:.3f}")
    
    print(f"\n🔍 Obserwacje:")
    print(f"- Całkowita liczba unikalnych słów: {len(word_freq_filtered)}")
    print(f"- Słowa treściowe są bardziej widoczne")
    print(f"- Lepszy fokus na kluczowych pojęciach")
    
except Exception as e:
    print(f"❌ Błąd podczas generowania word cloud: {e}")

# Porównanie side-by-side
try:
    if 'wordcloud_original' in locals() and 'wordcloud_filtered' in locals():
        print("-" * 50)
        print("PORÓWNANIE WORD CLOUDS")
        print("-" * 50)
        
        # Wyświetlanie obu word clouds obok siebie
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # Word cloud oryginalny
        ax1.imshow(wordcloud_original, interpolation='bilinear')
        ax1.axis('off')
        ax1.set_title('Tekst oryginalny\n(z stop words)', fontsize=14, fontweight='bold')
        
        # Word cloud bez stop words
        ax2.imshow(wordcloud_filtered, interpolation='bilinear')
        ax2.axis('off')
        ax2.set_title('Tekst bez stop words\n(tylko treść)', fontsize=14, fontweight='bold')
        
        plt.suptitle('Porównanie Word Clouds', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        # Analiza różnic
        print(f"\n📊 Analiza porównawcza:")
        
        # Porównanie liczby unikalnych słów
        original_words = set(word_freq_original.keys())
        filtered_words = set(word_freq_filtered.keys())
        
        print(f"\n📈 Statystyki słownika:")
        print(f"- Tekst oryginalny: {len(original_words)} unikalnych słów")
        print(f"- Tekst bez stop words: {len(filtered_words)} unikalnych słów")
        print(f"- Redukcja: {len(original_words) - len(filtered_words)} słów ({((len(original_words) - len(filtered_words)) / len(original_words) * 100):.1f}%)")
        
        # Słowa wspólne i unikalne
        common_words = original_words.intersection(filtered_words)
        only_original = original_words - filtered_words
        only_filtered = filtered_words - original_words
        
        print(f"\n🔍 Analiza zawartości:")
        print(f"- Słowa wspólne: {len(common_words)}")
        print(f"- Tylko w oryginalnym: {len(only_original)} (prawdopodobnie stop words)")
        print(f"- Tylko w filtrowanym: {len(only_filtered)} (może być 0)")
        
        if only_original:
            print(f"\n🛑 Usunięte słowa (stop words): {list(only_original)[:10]}")
        
        # Top słowa w obu wersjach
        print(f"\n🏆 Porównanie top 5 słów:")
        print(f"{'Oryginalny':<15} {'Bez stop words':<15}")
        print("-" * 30)
        
        top_original = [word for word, _ in sorted_words[:5]]
        top_filtered = [word for word, _ in sorted_words_filtered[:5]]
        
        for i in range(5):
            orig = top_original[i] if i < len(top_original) else ""
            filt = top_filtered[i] if i < len(top_filtered) else ""
            print(f"{orig:<15} {filt:<15}")
            
    else:
        print("⚠️ Nie można porównać - brak jednego z word clouds")
        
except Exception as e:
    print(f"❌ Błąd podczas porównania: {e}")

# Generowanie word cloud z dodatkowymi opcjami customizacji
print("-" * 50)
print("ZAAWANSOWANE WORD CLOUDS")
print("-" * 50)

try:
    # Word cloud z częstościami z TF-IDF (jeśli dostępne)
    if 'tfidf_filtered' in locals():
        print("\n🎯 Word Cloud oparty na wagach TF-IDF:")
        
        # Tworzenie słownika częstości z TF-IDF
        tfidf_dict = dict(zip(tfidf_filtered['word'], tfidf_filtered['avg_tfidf']))
        
        # Konfiguracja dla TF-IDF word cloud
        wordcloud_tfidf_config = {
            'width': 800,
            'height': 400,
            'max_words': 50,
            'background_color': 'white',
            'colormap': 'coolwarm',
            'relative_scaling': 0.8,
            'min_font_size': 12,
            'random_state': 42
        }
        
        wordcloud_tfidf = WordCloud(**wordcloud_tfidf_config).generate_from_frequencies(tfidf_dict)
        
        plt.figure(figsize=(12, 6))
        plt.imshow(wordcloud_tfidf, interpolation='bilinear')
        plt.axis('off')
        plt.title('Word Cloud oparty na wagach TF-IDF\n(rozmiar = ważność słowa)', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        print(f"✅ Word cloud TF-IDF wygenerowany")
        print(f"- Rozmiar słów odpowiada ich ważności TF-IDF")
        print(f"- Najważniejsze słowa są największe")
        
    else:
        print("⚠️ Dane TF-IDF niedostępne - pomijam zaawansowany word cloud")
    
    # Word cloud z różnymi kształtami (opcjonalnie)
    print(f"\n🎨 Dodatkowe opcje customizacji:")
    print(f"- Różne palety kolorów: viridis, plasma, coolwarm, tab10")
    print(f"- Kształty: można użyć masek obrazów")
    print(f"- Czcionki: można zmienić font_path")
    print(f"- Orientacje: prefer_horizontal, max_font_size")
    
except Exception as e:
    print(f"❌ Błąd w zaawansowanych word clouds: {e}")

print("=" * 70)
print("WNIOSKI Z ANALIZY WORD CLOUD")
print("=" * 70)

print(f"""
📊 GŁÓWNE OBSERWACJE:

1. 🎯 WPŁYW STOP WORDS:
   - Tekst oryginalny: Stop words dominują wizualizację
   - Tekst filtrowany: Lepszy fokus na treści merytorycznej
   - Redukcja szumu: Usunięcie ~30-40% słów poprawia czytelność

2. 🔍 IDENTYFIKACJA KLUCZOWYCH POJĘĆ:
   - Bez filtrowania: 'the', 'to', 'and' dominują
   - Po filtrowaniu: 'Emily', 'TechCorp', 'meeting', 'conference' widoczne
   - Lepsze wyróżnienie nazw własnych i rzeczowników

3. 📈 JAKOŚĆ WIZUALIZACJI:
   - Word cloud bez stop words jest bardziej informacyjny
   - Łatwiejsza identyfikacja głównych tematów
   - Lepsze proporcje między słowami treściowymi

4. 🎨 OPCJE CUSTOMIZACJI:
   - TF-IDF weighting: Jeszcze lepsze wyróżnienie ważnych słów
   - Różne palety kolorów: Poprawa czytelności
   - Kontrola rozmiaru: Optymalizacja dla różnych zastosowań

💡 REKOMENDACJE:

✅ NAJLEPSZE PRAKTYKI:
- Zawsze używaj tekstu bez stop words dla word cloud
- Rozważ użycie wag TF-IDF zamiast prostych częstości
- Dostosuj max_words do rozmiaru tekstu (50-100 dla krótkich tekstów)
- Użyj kontrastowych kolorów dla lepszej czytelności

🎯 ZASTOSOWANIA:
- Szybki przegląd głównych tematów dokumentu
- Prezentacje i raporty analityczne
- Identyfikacja kluczowych słów w korpusie
- Porównanie różnych dokumentów lub wersji tekstu

⚠️ OGRANICZENIA:
- Brak kontekstu i relacji między słowami
- Może pomijać ważne ale rzadkie terminy
- Nie pokazuje sentymentu ani intencji
- Wymaga preprocessing dla optymalnych wyników
""")

print(f"\n✅ Word Cloud analiza zakończona!")
print(f"📊 Wygenerowano wizualizacje pokazujące wpływ preprocessing na fokus treściowy")
print(f"🎯 Dane gotowe do dalszej analizy i prezentacji wyników")