import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer, PorterStemmer, SnowballStemmer

# Download necessary NLTK resources
nltk.download("punkt")
nltk.download("stopwords")
nltk.download("wordnet")
nltk.download("averaged_perceptron_tagger_eng")  # tagger POS

# Input text
text = "Last week, <PERSON> traveled from London to New York for a meeting with TechCorp. She stayed at the Hilton Hotel and prepared a presentation about the new AI software her team developed. On Friday, she met with <PERSON>, the CEO of TechCorp, to discuss a potential partnership. The meeting went well, but <PERSON> was exhausted after the long flight. She plans to visit Paris next month for another conference."

print("Original text:")
print(text)

# Podział tekstu na zdania
sentences = sent_tokenize(text)
print("Podział na zdania:")
for i, sentence in enumerate(sentences):
    print(f"Zdanie {i+1}: {sentence}")

# Tokenizacja każdego zdania
print("Tokenizacja zdań:")
for i, sentence in enumerate(sentences):
    tokens = word_tokenize(sentence)
    print(f"Zdanie {i+1} tokeny: {tokens}")

# Tokenizacja całego tekstu
all_tokens = word_tokenize(text)
print("Wszystkie tokeny:")
print(all_tokens)
print(f"Liczba tokenów: {len(all_tokens)}")

# Wybrane zdanie: "On Friday, she met with Robert Davis, the CEO of TechCorp, to discuss a potential partnership."
selected_sentence = sentences[2]  # Trzecie zdanie w tekście
print("Etap 2: Analiza morfologiczna (POS tagging)")
print(f'Wybrane zdanie: "{selected_sentence}"')

# Tokenizacja wybranego zdania
tokens = word_tokenize(selected_sentence)

# POS tagging
tagged_tokens = nltk.pos_tag(tokens)
print(f"\nTokeny: {tokens}")
print(f"Tagged tokens: {tagged_tokens}")

# Wyjaśnienie znaczenia tagów POS
pos_descriptions = {
    "CC": "Coordinating conjunction",
    "CD": "Cardinal number",
    "DT": "Determiner",
    "EX": "Existential there",
    "FW": "Foreign word",
    "IN": "Preposition or subordinating conjunction",
    "JJ": "Adjective",
    "JJR": "Adjective, comparative",
    "JJS": "Adjective, superlative",
    "LS": "List item marker",
    "MD": "Modal",
    "NN": "Noun, singular or mass",
    "NNS": "Noun, plural",
    "NNP": "Proper noun, singular",
    "NNPS": "Proper noun, plural",
    "PDT": "Predeterminer",
    "POS": "Possessive ending",
    "PRP": "Personal pronoun",
    "PRP$": "Possessive pronoun",
    "RB": "Adverb",
    "RBR": "Adverb, comparative",
    "RBS": "Adverb, superlative",
    "RP": "Particle",
    "SYM": "Symbol",
    "TO": "to",
    "UH": "Interjection",
    "VB": "Verb, base form",
    "VBD": "Verb, past tense",
    "VBG": "Verb, gerund or present participle",
    "VBN": "Verb, past participle",
    "VBP": "Verb, non-3rd person singular present",
    "VBZ": "Verb, 3rd person singular present",
    "WDT": "Wh-determiner",
    "WP": "Wh-pronoun",
    "WP$": "Possessive wh-pronoun",
    "WRB": "Wh-adverb",
    ",": "Comma",
    ".": "Period",
    ":": "Colon or semicolon",
    "$": "Dollar sign",
    "#": "Pound sign",
    '"': "Quote",
    "(": "Left parenthesis",
    ")": "Right parenthesis",
}

print("Tokeny z oznaczeniami części mowy:")
for token, tag in tagged_tokens:
    print(f"{token}: {tag} - {pos_descriptions.get(tag, 'Unknown tag')}")

# Wybrane zdanie: "She plans to visit Paris next month for another conference."
lemma_sentence = sentences[4]  # Piąte zdanie w tekście
print("Etap 3: Lematyzacja")
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
lemma_tokens = word_tokenize(lemma_sentence)

# POS tagging dla lematyzacji
lemma_tagged = nltk.pos_tag(lemma_tokens)

# Inicjalizacja lematyzatora
lemmatizer = WordNetLemmatizer()

print(f"\nTokeny: {lemma_tokens}")
print(f"Tagged tokens: {lemma_tagged}")

# Funkcja do konwersji tagów NLTK na tagi WordNet
def get_wordnet_pos(tag):
    if tag.startswith("J"):
        return "a"  # adjective
    elif tag.startswith("V"):
        return "v"  # verb
    elif tag.startswith("N"):
        return "n"  # noun
    elif tag.startswith("R"):
        return "r"  # adverb
    else:
        return "n"  # default to noun

# Lematyzacja z uwzględnieniem części mowy
lemmatized_tokens = []
for token, tag in lemma_tagged:
    wordnet_pos = get_wordnet_pos(tag)
    lemma = lemmatizer.lemmatize(token, pos=wordnet_pos)
    lemmatized_tokens.append((token, lemma, tag))

# Wyświetlenie wyników lematyzacji
print("Wyniki lematyzacji:")
print("Token | Lemat | POS Tag")
print("-" * 40)
for token, lemma, tag in lemmatized_tokens:
    print(f"{token} | {lemma} | {tag}")

# Porównanie oryginalnego zdania z lematyzowanym
original_tokens = [token for token, _, _ in lemmatized_tokens]
lemmatized_text = [lemma for _, lemma, _ in lemmatized_tokens]

print("Zdanie oryginalne:")
print(" ".join(original_tokens))
print("\nZdanie po lematyzacji:")
print(" ".join(lemmatized_text))

print("=" * 60)
print("Etap 4: Stemming")
print("=" * 60)
print(f'Wybrane zdanie: "{lemma_sentence}"')

# Tokenizacja wybranego zdania
stem_tokens = word_tokenize(lemma_sentence)

# Inicjalizacja stemmerów
porter_stemmer = PorterStemmer()
snowball_stemmer = SnowballStemmer('english')

print(f"\nTokeny: {stem_tokens}")

# Stemming z Porter Stemmer
porter_stemmed = []
for token in stem_tokens:
    stemmed = porter_stemmer.stem(token)
    porter_stemmed.append((token, stemmed))

# Wyświetlenie wyników Porter Stemmer
print("Wyniki Porter Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in porter_stemmed:
    print(f"{token} | {stem}")

# Stemming z Snowball Stemmer
snowball_stemmed = []
for token in stem_tokens:
    stemmed = snowball_stemmer.stem(token)
    snowball_stemmed.append((token, stemmed))

# Wyświetlenie wyników Snowball Stemmer
print("Wyniki Snowball Stemmer:")
print("Token | Stem")
print("-" * 25)
for token, stem in snowball_stemmed:
    print(f"{token} | {stem}")

# Porównanie zdań po stemmingu
porter_text = [stem for _, stem in porter_stemmed]
snowball_text = [stem for _, stem in snowball_stemmed]

print("Zdanie oryginalne:")
print(" ".join(stem_tokens))
print("\nZdanie po Porter Stemming:")
print(" ".join(porter_text))
print("\nZdanie po Snowball Stemming:")
print(" ".join(snowball_text))

print("=" * 60)
print("Porównanie Stemming vs Lematyzacja")
print("=" * 60)
print("Token | Porter Stem | Snowball Stem | Lemma")
print("-" * 50)

# Pobieramy lematy z poprzedniego etapu dla porównania
lemma_dict = {token: lemma for token, lemma, _ in lemmatized_tokens}

for token in stem_tokens:
    porter_stem = porter_stemmer.stem(token)
    snowball_stem = snowball_stemmer.stem(token)
    lemma = lemma_dict.get(token, token)  # Jeśli nie ma lematu, użyj oryginalnego tokenu
    print(f"{token} | {porter_stem} | {snowball_stem} | {lemma}")