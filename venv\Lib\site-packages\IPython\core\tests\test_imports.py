# encoding: utf-8

def test_import_completer():
    from IPython.core import completer

def test_import_crashhandler():
    from IPython.core import crashhandler

def test_import_debugger():
    from IPython.core import debugger

def test_import_excolors():
    from IPython.core import excolors

def test_import_history():
    from IPython.core import history

def test_import_hooks():
    from IPython.core import hooks

def test_import_getipython():
    from IPython.core import getipython

def test_import_interactiveshell():
    from IPython.core import interactiveshell

def test_import_logger():
    from IPython.core import logger

def test_import_macro():
    from IPython.core import macro

def test_import_magic():
    from IPython.core import magic

def test_import_oinspect():
    from IPython.core import oinspect

def test_import_prefilter():
    from IPython.core import prefilter

def test_import_prompts():
    from IPython.core import prompts

def test_import_release():
    from IPython.core import release

def test_import_ultratb():
    from IPython.core import ultratb

def test_import_usage():
    from IPython.core import usage
