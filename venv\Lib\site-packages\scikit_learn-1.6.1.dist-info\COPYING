BSD 3-Clause License

Copyright (c) 2007-2024 The scikit-learn developers.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

----

This binary distribution of scikit-learn also bundles the following software:

----

Name: Microsoft Visual C++ Runtime Files
Files: sklearn\.libs\*.dll
Availability: https://learn.microsoft.com/en-us/visualstudio/releases/2015/2015-redistribution-vs

Subject to the License Terms for the software, you may copy and distribute with your
program any of the files within the followng folder and its subfolders except as noted
below. You may not modify these files.

C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\redist

You may not distribute the contents of the following folders:

C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\redist\debug_nonredist
C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\redist\onecore\debug_nonredist

Subject to the License Terms for the software, you may copy and distribute the following
files with your program in your program’s application local folder or by deploying them
into the Global Assembly Cache (GAC):

VC\atlmfc\lib\mfcmifc80.dll
VC\atlmfc\lib\amd64\mfcmifc80.dll
